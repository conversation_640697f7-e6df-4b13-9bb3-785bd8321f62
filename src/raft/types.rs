use openraft::{BasicNode, Raft};
use serde::{Deserialize, Serialize};
use std::collections::BTreeMap;

/// Node ID type for the Raft cluster
pub type NodeId = u64;

/// Node information for cluster membership
pub type Node = BasicNode;

/// Configuration namespace identifier
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct ConfigNamespace {
    pub tenant: String,
    pub app: String,
    pub env: String,
}

impl std::fmt::Display for ConfigNamespace {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}/{}/{}", self.tenant, self.app, self.env)
    }
}

/// Configuration format enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ConfigFormat {
    <PERSON><PERSON>,
    Yam<PERSON>,
    <PERSON><PERSON>,
    Properties,
    Xml,
}

/// Core configuration metadata
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub id: u64,
    pub namespace: ConfigNamespace,
    pub name: String,
    pub latest_version_id: u64,
    pub releases: Vec<Release>,
    pub schema: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// Immutable configuration version
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigVersion {
    pub id: u64,
    pub config_id: u64,
    pub content: Vec<u8>,
    pub content_hash: String,
    pub format: ConfigFormat,
    pub creator_id: u64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub description: String,
}

/// Release rule for configuration deployment
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct Release {
    pub labels: BTreeMap<String, String>,
    pub version_id: u64,
    pub priority: i32,
}

/// Raft command enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RaftCommand {
    CreateConfig {
        namespace: ConfigNamespace,
        name: String,
        content: Vec<u8>,
        format: ConfigFormat,
        schema: Option<String>,
        creator_id: u64,
        description: String,
    },
    CreateVersion {
        config_id: u64,
        content: Vec<u8>,
        creator_id: u64,
        description: String,
    },
    UpdateReleaseRules {
        config_id: u64,
        releases: Vec<Release>,
    },
    DeleteConfig {
        config_id: u64,
    },
}

/// Client request wrapper for Raft
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientRequest {
    pub command: RaftCommand,
}

/// Client response for write operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientWriteResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

// Declare Raft types using openraft macro
openraft::declare_raft_types!(
    pub TypeConfig:
        D = ClientRequest,
        R = ClientWriteResponse,
        NodeId = NodeId,
        Node = Node,
        SnapshotData = std::io::Cursor<Vec<u8>>,
);

/// Type alias for the Raft instance
pub type ConfluxRaft = Raft<TypeConfig>;

/// Configuration key type for internal storage
pub type ConfigKey = String;

/// Helper function to create config key
pub fn make_config_key(namespace: &ConfigNamespace, name: &str) -> ConfigKey {
    format!("{}/{}", namespace, name)
}

/// Helper function to create config ID key
pub fn make_config_id_key(config_id: u64) -> Vec<u8> {
    let mut key = vec![0x02];
    key.extend_from_slice(&config_id.to_be_bytes());
    key
}

/// Helper function to create version key
pub fn make_version_key(config_id: u64, version_id: u64) -> Vec<u8> {
    let mut key = vec![0x03];
    key.extend_from_slice(&config_id.to_be_bytes());
    key.extend_from_slice(&version_id.to_be_bytes());
    key
}

/// Helper function to create name index key
pub fn make_name_index_key(namespace: &ConfigNamespace, name: &str) -> Vec<u8> {
    let mut key = vec![0x04];
    let name_key = format!("{}/{}", namespace, name);
    key.extend_from_slice(name_key.as_bytes());
    key
}

/// Helper function to create reverse index key
pub fn make_reverse_index_key(config_id: u64) -> Vec<u8> {
    let mut key = vec![0x05];
    key.extend_from_slice(&config_id.to_be_bytes());
    key
}
